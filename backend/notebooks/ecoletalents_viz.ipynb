import pandas as pd
import json
import os
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go

# 1. Chargement des données JSON
json_file = 'ecole_talents_data.json'

with open(json_file, 'r', encoding='utf-8') as f:
    json_data = json.load(f)

print(f"Données JSON chargées depuis {json_file}")

# 2. Conversion JSON vers DataFrame
if isinstance(json_data, list):
    df = pd.DataFrame(json_data)
else:
    df = pd.DataFrame([json_data])

print(f"DataFrame créé: {df.shape}")
print(df.head())

# 3. Sauvegarde en CSV
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
csv_filename = f'ecole_talents_{timestamp}.csv'

os.makedirs('../exports', exist_ok=True)
csv_filepath = f'../exports/{csv_filename}'

df.to_csv(csv_filepath, index=False, encoding='utf-8')
print(f"CSV sauvegardé: {csv_filepath}")
print(f"Nombre de lignes: {len(df)}")

# 4. Lecture du CSV sauvegardé
df_loaded = pd.read_csv(csv_filepath)

print(f"CSV rechargé: {df_loaded.shape}")
print("Aperçu des données:")
print(df_loaded.head())

# 5. Extraction des données pour visualisation
formations = []
for _, row in df_loaded.iterrows():
    data = eval(row['data'])  # Convertir string JSON en dict
    formations.append({
        'formation': row['title'],
        'total_students': data['total_students'],
        'active_programs': data['active_programs']
    })

df_viz = pd.DataFrame(formations)
print(df_viz)

# 6. Visualisations avec Plotly
# Graphique des étudiants par formation
fig1 = px.bar(df_viz, x='formation', y='total_students', 
              title='Nombre d\'étudiants par formation',
              color='total_students')
fig1.show()

# Graphique des programmes actifs
fig2 = px.pie(df_viz, values='active_programs', names='formation',
              title='Répartition des programmes actifs')
fig2.show()

# 7. Graphique combiné
fig3 = go.Figure()
fig3.add_trace(go.Bar(name='Étudiants', x=df_viz['formation'], y=df_viz['total_students']))
fig3.add_trace(go.Bar(name='Programmes', x=df_viz['formation'], y=df_viz['active_programs']))
fig3.update_layout(title='Étudiants vs Programmes par formation', barmode='group')
fig3.show()

# 8. Prêt pour les analyses
print("\nDonnées prêtes pour l'analyse!")
print(f"DataFrame disponible: df_loaded")
print(f"DataFrame visualisation: df_viz")
print(f"Fichier CSV: {csv_filepath}")