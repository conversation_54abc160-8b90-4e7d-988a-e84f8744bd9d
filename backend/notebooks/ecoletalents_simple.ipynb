import pandas as pd
import json
import os
from datetime import datetime

# 1. Chargement des données JSON
json_file = 'ecole_talents_data.json'  # Remplacez par votre fichier

with open(json_file, 'r', encoding='utf-8') as f:
    json_data = json.load(f)

print(f"Données JSON chargées depuis {json_file}")

# 2. Conversion JSON vers DataFrame
if isinstance(json_data, list):
    df = pd.DataFrame(json_data)
else:
    df = pd.DataFrame([json_data])

print(f"DataFrame créé: {df.shape}")
print(df.head())

# 3. Sauvegarde en CSV
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
csv_filename = f'ecole_talents_{timestamp}.csv'

os.makedirs('../exports', exist_ok=True)
csv_filepath = f'../exports/{csv_filename}'

df.to_csv(csv_filepath, index=False, encoding='utf-8')
print(f"CSV sauvegardé: {csv_filepath}")
print(f"Nombre de lignes: {len(df)}")

# 4. Lecture du CSV sauvegardé
df_loaded = pd.read_csv(csv_filepath)

print(f"CSV rechargé: {df_loaded.shape}")
print("Aperçu des données:")
print(df_loaded.head())
print("\nTypes de colonnes:")
print(df_loaded.dtypes)

# 5. Prêt pour les analyses
print("\nDonnées prêtes pour l'analyse!")
print(f"DataFrame disponible: df_loaded")
print(f"Fichier CSV: {csv_filepath}")

