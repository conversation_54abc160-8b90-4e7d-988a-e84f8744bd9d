import pandas as pd
import json
import xml.etree.ElementTree as ET
from pathlib import Path
import os

def load_csv(file_path):
    return pd.read_csv(file_path)

def json_to_csv(json_file, csv_file=None):
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if isinstance(data, list):
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame([data])
    
    if csv_file is None:
        csv_file = Path(json_file).with_suffix('.csv')
    
    df.to_csv(csv_file, index=False)
    return df

def xml_to_csv(xml_file, csv_file=None):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    
    data = []
    for child in root:
        row = {}
        for elem in child:
            row[elem.tag] = elem.text
        data.append(row)
    
    df = pd.DataFrame(data)
    
    if csv_file is None:
        csv_file = Path(xml_file).with_suffix('.csv')
    
    df.to_csv(csv_file, index=False)
    return df

# Exemple d'utilisation
file_path = 'data.csv'  # Remplacez par votre fichier

if os.path.exists(file_path):
    df = load_csv(file_path)
    print(f"CSV chargé: {df.shape}")
    print(df.head())
else:
    print(f"Fichier non trouvé: {file_path}")

# Conversion JSON vers CSV
json_file = 'data.json'  # Remplacez par votre fichier

if os.path.exists(json_file):
    df = json_to_csv(json_file)
    print(f"JSON converti: {df.shape}")
    print(df.head())
else:
    print(f"Fichier JSON non trouvé: {json_file}")

# Conversion XML vers CSV
xml_file = 'data.xml'  # Remplacez par votre fichier

if os.path.exists(xml_file):
    df = xml_to_csv(xml_file)
    print(f"XML converti: {df.shape}")
    print(df.head())
else:
    print(f"Fichier XML non trouvé: {xml_file}")