# Imports nécessaires
import os
import sys
import django
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configuration de l'affichage
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('seaborn-v0_8')
sns.set_palette('husl')

print("Imports terminés avec succès")

# Configuration Django
sys.path.append('../backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')
django.setup()

# Import du modèle
from api.models import DQEData

print("Configuration Django terminée")
print(f"Modèle DQEData importé avec succès")

# Chargement des données
print("Chargement des données du modèle DQEData...")

# Récupération de toutes les données
queryset = DQEData.objects.all()
total_count = queryset.count()

print(f"Nombre total d'enregistrements: {total_count}")

if total_count == 0:
    print("Aucune donnée trouvée dans le modèle DQEData")
    print("Conseil: Ajoutez des données via l'API ou l'admin Django")
else:
    print("Données chargées avec succès")

# Conversion en DataFrame pandas
if total_count > 0:
    # Conversion des données en DataFrame
    data_list = list(queryset.values())
    df = pd.DataFrame(data_list)
    
    print("DataFrame créé avec succès")
    print(f"Dimensions: {df.shape[0]} lignes × {df.shape[1]} colonnes")
    print("\nColonnes disponibles:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i:2d}. {col}")
else:
    df = pd.DataFrame()
    print("DataFrame vide créé")

# Affichage des premières lignes
if not df.empty:
    print("Aperçu des données (5 premières lignes):")
    print("=" * 50)
    display(df.head())
    
    print("\nInformations sur le DataFrame:")
    print("=" * 50)
    df.info()
    
    print("\nStatistiques descriptives:")
    print("=" * 50)
    display(df.describe(include='all'))
else:
    print("Aucune donnée à afficher")

# Export en CSV
if not df.empty:
    # Création du nom de fichier avec timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'dqedata_{timestamp}.csv'
    filepath = f'../exports/{filename}'
    
    # Création du dossier exports s'il n'existe pas
    os.makedirs('../exports', exist_ok=True)
    
    # Export du DataFrame
    df.to_csv(filepath, index=False, encoding='utf-8')
    
    print(f"Données exportées avec succès")
    print(f"Fichier: {filepath}")
    print(f"{len(df)} lignes exportées")
    
    # Vérification du fichier
    file_size = os.path.getsize(filepath)
    print(f"Taille du fichier: {file_size:,} octets")
else:
    print("Aucune donnée à exporter")

# Analyse exploratoire des données
if not df.empty:
    print("ANALYSE EXPLORATOIRE DES DONNÉES")
    print("=" * 40)
    
    # Valeurs manquantes
    missing_values = df.isnull().sum()
    if missing_values.sum() > 0:
        print("\nValeurs manquantes:")
        for col, count in missing_values[missing_values > 0].items():
            percentage = (count / len(df)) * 100
            print(f"  {col}: {count} ({percentage:.1f}%)")
    else:
        print("\nAucune valeur manquante détectée")
    
    # Types de données
    print("\nTypes de données:")
    for col, dtype in df.dtypes.items():
        print(f"  {col}: {dtype}")
    
    # Doublons
    duplicates = df.duplicated().sum()
    if duplicates > 0:
        print(f"\n{duplicates} lignes dupliquées détectées")
    else:
        print("\nAucun doublon détecté")
else:
    print("Aucune donnée pour l'analyse")

# Visualisations
if not df.empty and len(df) > 1:
    print("VISUALISATIONS")
    print("=" * 30)
    
    # Configuration des graphiques
    plt.figure(figsize=(15, 10))
    
    # Graphique 1: Distribution des données numériques
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        plt.subplot(2, 2, 1)
        df[numeric_cols].hist(bins=20, alpha=0.7)
        plt.title('Distribution des variables numériques')
        plt.tight_layout()
    
    # Graphique 2: Évolution temporelle (si colonnes de date)
    date_cols = df.select_dtypes(include=['datetime64']).columns
    if len(date_cols) > 0 and len(df) > 1:
        plt.subplot(2, 2, 2)
        df.set_index(date_cols[0]).resample('D').size().plot()
        plt.title('Évolution temporelle des enregistrements')
        plt.xticks(rotation=45)
    
    # Graphique 3: Top 10 des valeurs (pour colonnes catégorielles)
    categorical_cols = df.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        plt.subplot(2, 2, 3)
        col = categorical_cols[0]
        top_values = df[col].value_counts().head(10)
        top_values.plot(kind='bar')
        plt.title(f'Top 10 - {col}')
        plt.xticks(rotation=45)
    
    # Graphique 4: Matrice de corrélation (si variables numériques)
    if len(numeric_cols) > 1:
        plt.subplot(2, 2, 4)
        correlation_matrix = df[numeric_cols].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
        plt.title('Matrice de corrélation')
    
    plt.tight_layout()
    plt.show()
    
    print("Visualisations générées avec succès")
else:
    print("Données insuffisantes pour les visualisations")