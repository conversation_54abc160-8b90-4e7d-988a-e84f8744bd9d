{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_file(file_path):\n", "    \"\"\"Charge un fichier selon son extension\"\"\"\n", "    file_path = Path(file_path)\n", "    ext = file_path.suffix.lower()\n", "    \n", "    if ext == '.csv':\n", "        return pd.read_csv(file_path)\n", "    elif ext == '.json':\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "        if isinstance(data, list):\n", "            return pd.DataFrame(data)\n", "        else:\n", "            return pd.DataFrame([data])\n", "    elif ext in ['.xlsx', '.xls']:\n", "        return pd.read_excel(file_path)\n", "    else:\n", "        raise ValueError(f\"Format non supporté: {ext}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_to_csv(input_file, output_file=None):\n", "    \"\"\"Convertit un fichier vers CSV\"\"\"\n", "    df = load_file(input_file)\n", "    \n", "    if output_file is None:\n", "        input_path = Path(input_file)\n", "        output_file = input_path.with_suffix('.csv')\n", "    \n", "    df.to_csv(output_file, index=False, encoding='utf-8')\n", "    print(f\"Fichier converti: {output_file}\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exemple d'utilisation\n", "# Remplacez 'votre_fichier.json' par le chemin de votre fichier\n", "file_path = 'votre_fichier.json'\n", "\n", "if os.path.exists(file_path):\n", "    df = convert_to_csv(file_path)\n", "    print(f\"Shape: {df.shape}\")\n", "    print(df.head())\n", "else:\n", "    print(f\"Fichier non trouvé: {file_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Conversion multiple\n", "def convert_folder(folder_path, target_format='csv'):\n", "    \"\"\"Convertit tous les fichiers d'un dossier\"\"\"\n", "    folder = Path(folder_path)\n", "    supported_formats = ['.json', '.xlsx', '.xls']\n", "    \n", "    for file_path in folder.iterdir():\n", "        if file_path.suffix.lower() in supported_formats:\n", "            try:\n", "                convert_to_csv(file_path)\n", "            except Exception as e:\n", "                print(f\"Erreur avec {file_path}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Utilisation pour un dossier\n", "# convert_folder('path/to/your/folder')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}