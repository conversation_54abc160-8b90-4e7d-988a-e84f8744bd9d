{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import pandas as pd\n",
    "import json\n",
    "import xml.etree.ElementTree as ET\n",
    "from pathlib import Path\n",
    "import os"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def load_csv(file_path):\n",
    "    return pd.read_csv(file_path)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def json_to_csv(json_file, csv_file=None):\n",
    "    with open(json_file, 'r', encoding='utf-8') as f:\n",
    "        data = json.load(f)\n",
    "    \n",
    "    if isinstance(data, list):\n",
    "        df = pd.DataFrame(data)\n",
    "    else:\n",
    "        df = pd.DataFrame([data])\n",
    "    \n",
    "    if csv_file is None:\n",
    "        csv_file = Path(json_file).with_suffix('.csv')\n",
    "    \n",
    "    df.to_csv(csv_file, index=False)\n",
    "    return df"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def xml_to_csv(xml_file, csv_file=None):\n",
    "    tree = ET.parse(xml_file)\n",
    "    root = tree.getroot()\n",
    "    \n",
    "    data = []\n",
    "    for child in root:\n",
    "        row = {}\n",
    "        for elem in child:\n",
    "            row[elem.tag] = elem.text\n",
    "        data.append(row)\n",
    "    \n",
    "    df = pd.DataFrame(data)\n",
    "    \n",
    "    if csv_file is None:\n",
    "        csv_file = Path(xml_file).with_suffix('.csv')\n",
    "    \n",
    "    df.to_csv(csv_file, index=False)\n",
    "    return df"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Exemple d'utilisation\n",
    "file_path = 'data.csv'  # Remplacez par votre fichier\n",
    "\n",
    "if os.path.exists(file_path):\n",
    "    df = load_csv(file_path)\n",
    "    print(f\"CSV chargé: {df.shape}\")\n",
    "    print(df.head())\n",
    "else:\n",
    "    print(f\"Fichier non trouvé: {file_path}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Conversion JSON vers CSV\n",
    "json_file = 'data.json'  # Remplacez par votre fichier\n",
    "\n",
    "if os.path.exists(json_file):\n",
    "    df = json_to_csv(json_file)\n",
    "    print(f\"JSON converti: {df.shape}\")\n",
    "    print(df.head())\n",
    "else:\n",
    "    print(f\"Fichier JSON non trouvé: {json_file}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Conversion XML vers CSV\n",
    "xml_file = 'data.xml'  # Remplacez par votre fichier\n",
    "\n",
    "if os.path.exists(xml_file):\n",
    "    df = xml_to_csv(xml_file)\n",
    "    print(f\"XML converti: {df.shape}\")\n",
    "    print(df.head())\n",
    "else:\n",
    "    print(f\"Fichier XML non trouvé: {xml_file}\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
  {
   "cell_type": "code",
   "execution_count": 5,
   "metadata": {},
   "outputs": [
    {
     "ename": "NameError",
     "evalue": "name 'df' is not defined",
     "output_type": "error",
     "traceback": [
      "\u001b[0;31m---------------------------------------------------------------------------\u001b[0m",
      "\u001b[0;31mNameError\u001b[0m                                 Traceback (most recent call last)",
      "Cell \u001b[0;32mIn[5], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Export en CSV\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mdf\u001b[49m\u001b[38;5;241m.\u001b[39mempty:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;66;03m# Création du nom de fichier avec timestamp\u001b[39;00m\n\u001b[1;32m      4\u001b[0m     timestamp \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mnow()\u001b[38;5;241m.\u001b[39mstrftime(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mY\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mm\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mH\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mM\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mS\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      5\u001b[0m     filename \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mecoletalents_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtimestamp\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.csv\u001b[39m\u001b[38;5;124m'\u001b[39m\n",
      "\u001b[0;31mNameError\u001b[0m: name 'df' is not defined"
     ]
    }
   ],
   "source": [
    "# Export en CSV\n",
    "if not df.empty:\n",
    "    # Création du nom de fichier avec timestamp\n",
    "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n",
    "    filename = f'ecoletalents_{timestamp}.csv'\n",
    "    filepath = f'../exports/{filename}'\n",
    "    \n",
    "    # Création du dossier exports s'il n'existe pas\n",
    "    os.makedirs('../exports', exist_ok=True)\n",
    "    \n",
    "    # Export du DataFrame\n",
    "    df.to_csv(filepath, index=False, encoding='utf-8')\n",
    "    \n",
    "    print(f\"Données exportées avec succès\")\n",
    "    print(f\"Fichier: {filepath}\")\n",
    "    print(f\"{len(df)} lignes exportées\")\n",
    "    \n",
    "    # Vérification du fichier\n",
    "    file_size = os.path.getsize(filepath)\n",
    "    print(f\"Taille du fichier: {file_size:,} octets\")\n",
    "else:\n",
    "    print(\"Aucune donnée à exporter\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analyse exploratoire des données\n",
    "if not df.empty:\n",
    "    print(\"ANALYSE EXPLORATOIRE DES DONNÉES\")\n",
    "    print(\"=\" * 40)\n",
    "    \n",
    "    # Valeurs manquantes\n",
    "    missing_values = df.isnull().sum()\n",
    "    if missing_values.sum() > 0:\n",
    "        print(\"\\nValeurs manquantes:\")\n",
    "        for col, count in missing_values[missing_values > 0].items():\n",
    "            percentage = (count / len(df)) * 100\n",
    "            print(f\"  {col}: {count} ({percentage:.1f}%)\")\n",
    "    else:\n",
    "        print(\"\\nAucune valeur manquante détectée\")\n",
    "    \n",
    "    # Types de données\n",
    "    print(\"\\nTypes de données:\")\n",
    "    for col, dtype in df.dtypes.items():\n",
    "        print(f\"  {col}: {dtype}\")\n",
    "    \n",
    "    # Doublons\n",
    "    duplicates = df.duplicated().sum()\n",
    "    if duplicates > 0:\n",
    "        print(f\"\\n{duplicates} lignes dupliquées détectées\")\n",
    "    else:\n",
    "        print(\"\\nAucun doublon détecté\")\n",
    "else:\n",
    "    print(\"Aucune donnée pour l'analyse\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 6,
   "metadata": {},
   "outputs": [
    {
     "ename": "NameError",
     "evalue": "name 'df' is not defined",
     "output_type": "error",
     "traceback": [
      "\u001b[0;31m---------------------------------------------------------------------------\u001b[0m",
      "\u001b[0;31mNameError\u001b[0m                                 Traceback (most recent call last)",
      "Cell \u001b[0;32mIn[6], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Visualisations\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mdf\u001b[49m\u001b[38;5;241m.\u001b[39mempty \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(df) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mVISUALISATIONS\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m30\u001b[39m)\n",
      "\u001b[0;31mNameError\u001b[0m: name 'df' is not defined"
     ]
    }
   ],
   "source": [
    "# Visualisations\n",
    "if not df.empty and len(df) > 1:\n",
    "    print(\"VISUALISATIONS\")\n",
    "    print(\"=\" * 30)\n",
    "    \n",
    "    # Configuration des graphiques\n",
    "    plt.figure(figsize=(15, 10))\n",
    "    \n",
    "    # Graphique 1: Distribution des données numériques\n",
    "    numeric_cols = df.select_dtypes(include=[np.number]).columns\n",
    "    if len(numeric_cols) > 0:\n",
    "        plt.subplot(2, 2, 1)\n",
    "        df[numeric_cols].hist(bins=20, alpha=0.7)\n",
    "        plt.title('Distribution des variables numériques')\n",
    "        plt.tight_layout()\n",
    "    \n",
    "    # Graphique 2: Évolution temporelle (si colonnes de date)\n",
    "    date_cols = df.select_dtypes(include=['datetime64']).columns\n",
    "    if len(date_cols) > 0 and len(df) > 1:\n",
    "        plt.subplot(2, 2, 2)\n",
    "        df.set_index(date_cols[0]).resample('D').size().plot()\n",
    "        plt.title('Évolution temporelle des enregistrements')\n",
    "        plt.xticks(rotation=45)\n",
    "    \n",
    "    # Graphique 3: Top 10 des valeurs (pour colonnes catégorielles)\n",
    "    categorical_cols = df.select_dtypes(include=['object']).columns\n",
    "    if len(categorical_cols) > 0:\n",
    "        plt.subplot(2, 2, 3)\n",
    "        col = categorical_cols[0]\n",
    "        top_values = df[col].value_counts().head(10)\n",
    "        top_values.plot(kind='bar')\n",
    "        plt.title(f'Top 10 - {col}')\n",
    "        plt.xticks(rotation=45)\n",
    "    \n",
    "    # Graphique 4: Matrice de corrélation (si variables numériques)\n",
    "    if len(numeric_cols) > 1:\n",
    "        plt.subplot(2, 2, 4)\n",
    "        correlation_matrix = df[numeric_cols].corr()\n",
    "        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)\n",
    "        plt.title('Matrice de corrélation')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(\"Visualisations générées avec succès\")\n",
    "else:\n",
    "    print(\"Données insuffisantes pour les visualisations\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Résumé de l'analyse\n",
    "\n",
    "### Actions réalisées\n",
    "1. Chargement des données depuis le modèle Django\n",
    "2. Conversion en DataFrame pandas\n",
    "3. Export CSV avec timestamp\n",
    "4. Analyse exploratoire (valeurs manquantes, doublons, types)\n",
    "5. Visualisations automatiques\n",
    "\n",
    "### Informations du modèle\n",
    "- **Modèle**: EcoleTalents\n",
    "- **Description**: Données de l'école des talents\n",
    "- **Catégorie**: Formation\n",
    "- **Champs principaux**: title, description, data, created_at, updated_at\n",
    "\n",
    "### Prochaines étapes\n",
    "1. Analyser les résultats obtenus\n",
    "2. Identifier les patterns et tendances\n",
    "3. Créer des visualisations personnalisées\n",
    "4. Intégrer les insights dans le dashboard\n",
    "\n",
    "*Notebook généré automatiquement le 2025-07-08 12:14:30*"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.3"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
