{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Analyse des données - EcoleTalents\n", "\n", "**Description**: Données de l'école des talents\n", "**Catégorie**: Formation\n", "**Date de création**: 2025-07-08 12:14:30\n", "\n", "## Objectifs\n", "1. Charger les données du modèle depuis la base de données\n", "2. <PERSON><PERSON><PERSON><PERSON> les données sous forme de DataFrame pandas\n", "3. Exporter les données en format CSV\n", "4. <PERSON><PERSON> une analyse exploratoire des données\n", "5. <PERSON><PERSON><PERSON><PERSON> des visualisations\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'django'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01msys\u001b[39;00m\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'django'"]}], "source": ["# Imports nécessaires\n", "import os\n", "import sys\n", "import django\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configuration de l'affichage\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette('husl')\n", "\n", "print(\"Imports terminés avec succès\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'django' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m sys\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m../backend\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      3\u001b[0m os\u001b[38;5;241m.\u001b[39menviron\u001b[38;5;241m.\u001b[39mset<PERSON><PERSON>ult(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDJANGO_SETTINGS_MODULE\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mkay_analytics.settings\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m----> 4\u001b[0m \u001b[43mdjango\u001b[49m\u001b[38;5;241m.\u001b[39msetup()\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# Import du modèle\u001b[39;00m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mm<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m E<PERSON>leTalents\n", "\u001b[0;31mNameError\u001b[0m: name 'django' is not defined"]}], "source": ["# Configuration Django\n", "sys.path.append('../backend')\n", "os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')\n", "django.setup()\n", "\n", "# Import du modèle\n", "from api.models import EcoleTalents\n", "\n", "print(\"Configuration Django terminée\")\n", "print(f\"Modèle EcoleTalents importé avec succès\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chargement des données du modèle EcoleTalents...\n"]}, {"ename": "NameError", "evalue": "name 'EcoleTalents' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 5\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mChargement des données du modèle EcoleTalents...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# Récupération de toutes les données\u001b[39;00m\n\u001b[0;32m----> 5\u001b[0m queryset \u001b[38;5;241m=\u001b[39m \u001b[43mEcoleTalents\u001b[49m\u001b[38;5;241m.\u001b[39mobjects\u001b[38;5;241m.\u001b[39mall()\n\u001b[1;32m      6\u001b[0m total_count \u001b[38;5;241m=\u001b[39m queryset\u001b[38;5;241m.\u001b[39mcount()\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNombre total d\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124menregistrements: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtotal_count\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'EcoleTalents' is not defined"]}], "source": ["# Chargement des données\n", "print(\"Chargement des données du modèle EcoleTalents...\")\n", "\n", "# Récupération de toutes les données\n", "queryset = EcoleTalents.objects.all()\n", "total_count = queryset.count()\n", "\n", "print(f\"Nombre total d'enregistrements: {total_count}\")\n", "\n", "if total_count == 0:\n", "    print(\"Aucune donnée trouvée dans le modèle EcoleTalents\")\n", "    print(\"Conseil: Ajoutez des données via l'API ou l'admin Django\")\n", "else:\n", "    print(\"Données chargées avec succès\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'total_count' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Conversion en DataFrame pandas\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mtotal_count\u001b[49m \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;66;03m# Conversion des données en DataFrame\u001b[39;00m\n\u001b[1;32m      4\u001b[0m     data_list \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(queryset\u001b[38;5;241m.\u001b[39mvalues())\n\u001b[1;32m      5\u001b[0m     df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame(data_list)\n", "\u001b[0;31mNameError\u001b[0m: name 'total_count' is not defined"]}], "source": ["# Conversion en DataFrame pandas\n", "if total_count > 0:\n", "    # Conversion des données en DataFrame\n", "    data_list = list(queryset.values())\n", "    df = pd.DataFrame(data_list)\n", "    \n", "    print(\"DataFrame créé avec succès\")\n", "    print(f\"Dimensions: {df.shape[0]} lignes × {df.shape[1]} colonnes\")\n", "    print(\"\\nColonnes disponibles:\")\n", "    for i, col in enumerate(df.columns, 1):\n", "        print(f\"  {i:2d}. {col}\")\n", "else:\n", "    df = pd.DataFrame()\n", "    print(\"DataFrame vide créé\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Affichage des premières lignes\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mdf\u001b[49m\u001b[38;5;241m.\u001b[39mempty:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAperçu des données (5 premières lignes):\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m50\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'df' is not defined"]}], "source": ["# Affichage des premières lignes\n", "if not df.empty:\n", "    print(\"Aperçu des données (5 premières lignes):\")\n", "    print(\"=\" * 50)\n", "    display(df.head())\n", "    \n", "    print(\"\\nInformations sur le DataFrame:\")\n", "    print(\"=\" * 50)\n", "    df.info()\n", "    \n", "    print(\"\\nStatistiques descriptives:\")\n", "    print(\"=\" * 50)\n", "    display(df.describe(include='all'))\n", "else:\n", "    print(\"Aucune donnée à afficher\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Export en CSV\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mdf\u001b[49m\u001b[38;5;241m.\u001b[39mempty:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;66;03m# Création du nom de fichier avec timestamp\u001b[39;00m\n\u001b[1;32m      4\u001b[0m     timestamp \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mnow()\u001b[38;5;241m.\u001b[39mstrftime(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mY\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mm\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mH\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mM\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mS\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      5\u001b[0m     filename \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mecoletalents_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtimestamp\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.csv\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'df' is not defined"]}], "source": ["# Export en CSV\n", "if not df.empty:\n", "    # Création du nom de fichier avec timestamp\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    filename = f'ecoletalents_{timestamp}.csv'\n", "    filepath = f'../exports/{filename}'\n", "    \n", "    # Création du dossier exports s'il n'existe pas\n", "    os.makedirs('../exports', exist_ok=True)\n", "    \n", "    # Export du DataFrame\n", "    df.to_csv(filepath, index=False, encoding='utf-8')\n", "    \n", "    print(f\"Données exportées avec succès\")\n", "    print(f\"Fichier: {filepath}\")\n", "    print(f\"{len(df)} lignes exportées\")\n", "    \n", "    # Vé<PERSON> du fichier\n", "    file_size = os.path.getsize(filepath)\n", "    print(f\"<PERSON><PERSON> du fichier: {file_size:,} octets\")\n", "else:\n", "    print(\"Aucune donnée à exporter\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse exploratoire des données\n", "if not df.empty:\n", "    print(\"ANALYSE EXPLORATOIRE DES DONNÉES\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Valeurs manquantes\n", "    missing_values = df.isnull().sum()\n", "    if missing_values.sum() > 0:\n", "        print(\"\\nValeurs manquantes:\")\n", "        for col, count in missing_values[missing_values > 0].items():\n", "            percentage = (count / len(df)) * 100\n", "            print(f\"  {col}: {count} ({percentage:.1f}%)\")\n", "    else:\n", "        print(\"\\nAucune valeur manquante détectée\")\n", "    \n", "    # Types de données\n", "    print(\"\\nTypes de données:\")\n", "    for col, dtype in df.dtypes.items():\n", "        print(f\"  {col}: {dtype}\")\n", "    \n", "    # Doublons\n", "    duplicates = df.duplicated().sum()\n", "    if duplicates > 0:\n", "        print(f\"\\n{duplicates} lignes dupliquées détectées\")\n", "    else:\n", "        print(\"\\nAucun doublon détecté\")\n", "else:\n", "    print(\"Aucune donnée pour l'analyse\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Visualisations\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mdf\u001b[49m\u001b[38;5;241m.\u001b[39mempty \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(df) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mVISUALISATIONS\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m30\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'df' is not defined"]}], "source": ["# Visualisations\n", "if not df.empty and len(df) > 1:\n", "    print(\"VISUALISATIONS\")\n", "    print(\"=\" * 30)\n", "    \n", "    # Configuration des graphiques\n", "    plt.figure(figsize=(15, 10))\n", "    \n", "    # Graphique 1: Distribution des données numériques\n", "    numeric_cols = df.select_dtypes(include=[np.number]).columns\n", "    if len(numeric_cols) > 0:\n", "        plt.subplot(2, 2, 1)\n", "        df[numeric_cols].hist(bins=20, alpha=0.7)\n", "        plt.title('Distribution des variables numériques')\n", "        plt.tight_layout()\n", "    \n", "    # Graphique 2: Évolution temporelle (si colonnes de date)\n", "    date_cols = df.select_dtypes(include=['datetime64']).columns\n", "    if len(date_cols) > 0 and len(df) > 1:\n", "        plt.subplot(2, 2, 2)\n", "        df.set_index(date_cols[0]).resample('D').size().plot()\n", "        plt.title('Évolution temporelle des enregistrements')\n", "        plt.xticks(rotation=45)\n", "    \n", "    # Graphique 3: Top 10 des valeurs (pour colonnes catégorielles)\n", "    categorical_cols = df.select_dtypes(include=['object']).columns\n", "    if len(categorical_cols) > 0:\n", "        plt.subplot(2, 2, 3)\n", "        col = categorical_cols[0]\n", "        top_values = df[col].value_counts().head(10)\n", "        top_values.plot(kind='bar')\n", "        plt.title(f'Top 10 - {col}')\n", "        plt.xticks(rotation=45)\n", "    \n", "    # Graphique 4: <PERSON><PERSON>rr<PERSON> (si variables numériques)\n", "    if len(numeric_cols) > 1:\n", "        plt.subplot(2, 2, 4)\n", "        correlation_matrix = df[numeric_cols].corr()\n", "        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)\n", "        plt.title('<PERSON><PERSON> de corrélation')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Visualisations générées avec succès\")\n", "else:\n", "    print(\"Données insuffisantes pour les visualisations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> de l'analyse\n", "\n", "### Actions réalisées\n", "1. Chargement des données depuis le modèle Django\n", "2. Conversion en DataFrame pandas\n", "3. Export CSV avec timestamp\n", "4. Analyse exploratoire (valeurs manquantes, doublons, types)\n", "5. Visualisations automatiques\n", "\n", "### Informations du modèle\n", "- **Modè<PERSON>**: EcoleTalents\n", "- **Description**: Données de l'école des talents\n", "- **<PERSON><PERSON><PERSON><PERSON>**: Formation\n", "- **<PERSON>mps principaux**: title, description, data, created_at, updated_at\n", "\n", "### Prochaines étapes\n", "1. <PERSON><PERSON><PERSON> les résultats obtenus\n", "2. Identifier les patterns et tendances\n", "3. <PERSON><PERSON>er des visualisations personnalisées\n", "4. <PERSON><PERSON>grer les insights dans le dashboard\n", "\n", "*Notebook généré automatiquement le 2025-07-08 12:14:30*"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}