{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import xml.etree.ElementTree as ET\n", "from pathlib import Path\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_csv(file_path):\n", "    return pd.read_csv(file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def json_to_csv(json_file, csv_file=None):\n", "    with open(json_file, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    \n", "    if isinstance(data, list):\n", "        df = pd.DataFrame(data)\n", "    else:\n", "        df = pd.DataFrame([data])\n", "    \n", "    if csv_file is None:\n", "        csv_file = Path(json_file).with_suffix('.csv')\n", "    \n", "    df.to_csv(csv_file, index=False)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def xml_to_csv(xml_file, csv_file=None):\n", "    tree = ET.parse(xml_file)\n", "    root = tree.getroot()\n", "    \n", "    data = []\n", "    for child in root:\n", "        row = {}\n", "        for elem in child:\n", "            row[elem.tag] = elem.text\n", "        data.append(row)\n", "    \n", "    df = pd.DataFrame(data)\n", "    \n", "    if csv_file is None:\n", "        csv_file = Path(xml_file).with_suffix('.csv')\n", "    \n", "    df.to_csv(csv_file, index=False)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exemple d'utilisation\n", "file_path = 'data.csv'  # Remplacez par votre fichier\n", "\n", "if os.path.exists(file_path):\n", "    df = load_csv(file_path)\n", "    print(f\"CSV chargé: {df.shape}\")\n", "    print(df.head())\n", "else:\n", "    print(f\"Fichier non trouvé: {file_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Conversion JSON vers CSV\n", "json_file = 'data.json'  # Remplacez par votre fichier\n", "\n", "if os.path.exists(json_file):\n", "    df = json_to_csv(json_file)\n", "    print(f\"JSON converti: {df.shape}\")\n", "    print(df.head())\n", "else:\n", "    print(f\"Fichier JSON non trouvé: {json_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Conversion XML vers CSV\n", "xml_file = 'data.xml'  # Remplacez par votre fichier\n", "\n", "if os.path.exists(xml_file):\n", "    df = xml_to_csv(xml_file)\n", "    print(f\"XML converti: {df.shape}\")\n", "    print(df.head())\n", "else:\n", "    print(f\"Fichier XML non trouvé: {xml_file}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}